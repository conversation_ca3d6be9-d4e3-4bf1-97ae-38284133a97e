Database initialized at data/passchanger.db
Generated new encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
LLM connection test failed: 'name'
Failed to initialize AI engine: 'name'
Failed to initialize components: 'name'
Fatal error: 'name'
Database connections closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: []
Model deepseek-r1:32b not found. Available models: []
LLM connection test failed: No models available in Ollama
Failed to initialize AI engine: No models available in Ollama
Failed to initialize components: No models available in Ollama
Fatal error: No models available in Ollama
Database connections closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Database connections closed
Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x7f14cf73ae40>
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Database connections closed
Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x7f6694ecf1a0>
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Added account: GeekDadKevin
Scanning 1 accounts for leaks
HIBP API error 401 for <EMAIL>
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
